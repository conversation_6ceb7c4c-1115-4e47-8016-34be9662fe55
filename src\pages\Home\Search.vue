<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
</script>

<template>
  <div class="search">
    <el-autocomplete
      clearable
      placeholder="请输入医院名称"
      class="input" />
    <el-button :icon="Search"></el-button>
  </div>
</template>

<style scoped lang="scss">
.search {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  // 方法1: 直接设置 el-autocomplete 组件宽度
  :deep(.el-autocomplete) {
    width: 400px;
  }
}
</style>
